2025-07-20 20:28:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:28:16.423125",
  "exception_type": "AttributeError",
  "exception_message": "'MainWindow' object has no attribute 'switch_to_settings_tab'",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:28:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:28:18.447073",
  "exception_type": "AttributeError",
  "exception_message": "'MainWindow' object has no attribute 'switch_to_settings_tab'",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:34:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:34:38.604736",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:34:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:34:42.465801",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:34:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:34:47.570551",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:35:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:35:18.778858",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:35:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:35:25.331454",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:36:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:36:46.796622",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:36:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:36:51.026833",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:37:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:37:29.153097",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:38:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:38:24.034451",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:38:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:38:41.052459",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:38:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:38:54.477297",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:39:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:39:12.230249",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:39:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:39:30.422753",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:39:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:39:43.878653",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:05.687420",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:24.308970",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:32.036438",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:49.374662",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:59.224712",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:41:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:41:27.930876",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:41:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:41:40.293662",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:41:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:41:56.000652",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:42:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:42:28.772550",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:42:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:42:41.165362",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:42:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:42:56.133222",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:43:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:43:23.470992",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:43:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:43:42.600129",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:43:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:43:55.516541",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:44:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:44:12.630932",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:44:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:44:51.455145",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:44:57 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:44:57.949384",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:45:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:45:26.129689",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:46:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:46:06.161827",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:46:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:46:14.690761",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:46:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:46:27.696224",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:47:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:47:04.722971",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:47:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:47:15.747765",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:47:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:47:46.406420",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:48:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:48:25.472376",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:49:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:49:32.016816",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:49:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:49:38.918611",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:50:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:50:35.479953",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:34.237563",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:37.449209",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:40.760512",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:47.701055",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:52:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:52:38.872518",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:52:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:52:42.189800",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:52:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:52:48.869030",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:19.045980",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:22.300085",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:30.530878",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:52.346150",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:53.339244",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:53.344665",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:54.352718",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:54.354591",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:55.609051",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:39.557932",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:40.667440",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:40.674256",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:41.679170",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:41.680475",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:01.671946",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:02.768555",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:02.775345",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:03.801996",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:03.803601",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:45.797863",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:46.811812",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:46.817736",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:47.823533",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:47.824823",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:18.995121",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:20.028548",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:20.034929",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:21.047110",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:21.048650",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:58 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:58.120604",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:59.112634",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:59.119255",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:58:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:58:00.126098",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:58:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:58:00.127326",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:20.865261",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:21.866840",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:21.872426",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:22.880680",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:22.882166",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:39.575635",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:39.603955",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:40.615397",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:40.616523",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:16.194466",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:17.236053",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:17.241920",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:18.247213",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:18.259181",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:03:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:03:51.957263",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:05.024371",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:25.597923",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:26.712177",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:26.722370",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:27.729546",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:27.731248",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:19.180438",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:20.190454",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:20.196737",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:21.216093",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:21.217337",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:47.156594",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:10:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:10:29.912673",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:16.129143",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:18.786837",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:19.802276",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:19.808453",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:20.819747",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:20.821114",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:12:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:12:21.836075",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:12:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:12:21.837271",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:12:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:12:26.420825",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:38.199375",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:39.245988",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:39.252367",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:40.258805",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:40.260197",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:59.344303",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:10.042967",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:11.548399",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:11.564402",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:12.752363",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:12.754789",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:43.050577",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في إنشاء تقرير الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 323, in get_performance_report\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:47.557187",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحسين الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 377, in optimize_performance\n    smart_logger.log_info(f\"تم تنظيف {collected} كائن من الذاكرة\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:55.078209",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:04.175161",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:05.195901",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:05.201433",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:06.332662",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:06.333999",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:17.268404",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:36.396893",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:36.404652",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:37.385753",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:37.391689",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:38.422036",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:38.423401",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:17:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:17:34.792877",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:17:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:17:39.438499",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:17:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:17:39.440039",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:18:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:18:40.452287",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:18:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:18:40.453242",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:19:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:19:30.730488",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء النسخة الاحتياطية",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 62, in create_backup\n    smart_logger.log_info(f\"بدء إنشاء نسخة احتياطية من نوع: {backup_type}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:19:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:19:41.466207",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:19:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:19:41.467246",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:20:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:20:42.475441",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:20:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:20:42.476400",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:20:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:20:49.434500",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:03.612338",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في إنشاء تقرير الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 323, in get_performance_report\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:07.337125",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحسين الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 377, in optimize_performance\n    smart_logger.log_info(f\"تم تنظيف {collected} كائن من الذاكرة\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:43.489627",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:43.490590",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:22:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:22:19.115928",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:23:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:23:56.577132",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:40.758872",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:41.860658",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:41.866620",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:42.979149",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:42.980493",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:25:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:25:43.996212",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:25:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:25:43.997210",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:14.366020",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:19.994659",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:21.043249",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:21.048708",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:22.182500",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:22.185188",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:22.188291",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:37.258503",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:38.265667",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:38.271202",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:39.364470",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:39.365825",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:39.369324",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:43.268625",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:44.267574",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:44.273839",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:45.411667",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:45.412932",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:19.893200",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:23.087390",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:24.087518",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:24.093221",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:25.732282",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:25.746679",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:02.609819",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:10.750636",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:12.109512",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:13.103308",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:13.108879",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:14.215642",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:14.216976",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:15.228052",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:15.229161",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:25.266721",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:29.494549",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:30.529137",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:30.535441",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:31.556617",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:31.558277",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:52.179316",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:53.191851",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:53.197463",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:54.223062",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:54.224392",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:17.361763",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:19.247591",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:20.244126",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:20.250239",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:21.264350",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:21.268220",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:22.285525",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:22.288036",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:44.222513",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:49.084431",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:50 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:50.179201",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:50 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:50.184604",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:51.208449",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:51.209679",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:09.428150",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:15.056452",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:16.070011",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:16.076132",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:17.121262",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:17.122722",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:35:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:35:18.140415",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:35:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:35:18.143483",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:35:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:35:59.574020",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:36:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:36:19.160995",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:36:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:36:19.163330",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:36:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:36:36.265080",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:17.043445",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:40.397177",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:41.444628",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:41.450282",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:42.651014",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:42.652537",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:19.170639",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:43.669695",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:43.672984",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:55.373398",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:59.778602",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:00.799378",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:00.806201",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:01.844179",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:01.846537",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:41.595061",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:42.590913",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:42.596779",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:43.618339",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:43.619661",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:09.584895",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:10.648807",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:10.654622",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:11.671885",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:11.673354",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:27.934969",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:29.555055",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:30.556078",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:30.562095",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:31.569693",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:31.571163",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:32.584804",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:32.586137",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:42.487392",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:47.679358",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:48.678513",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:48.684384",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:49.823337",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:49.824892",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:10.405277",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:14.672050",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:15.686807",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:15.693410",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:16.770137",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:16.771610",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:43.119493",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:44.096872",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:44.103426",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:45.129527",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:45.131275",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:43:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:43:46.150076",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:43:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:43:46.153038",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:44:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:44:47.170983",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:44:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:44:47.174201",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:45:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:45:48.184622",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:45:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:45:48.185608",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:45:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:45:52.856907",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:48:58 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:48:58.307054",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:48:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:48:59.368097",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:48:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:48:59.374208",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:49:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:49:00.402296",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:49:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:49:00.403617",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:49:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:49:18.187612",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:49:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:49:20.894689",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:13.087685",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:14.117332",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:14.123232",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:15.243560",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:15.244834",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:51:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:51:16.255470",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:51:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:51:16.256819",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:51:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:51:20.861969",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:53:58 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:53:58.080523",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:53:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:53:59.103369",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:53:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:53:59.108947",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:54:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:54:00.128136",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:54:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:54:00.130749",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:54:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:54:43.816849",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء النسخة الاحتياطية",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 62, in create_backup\n    smart_logger.log_info(f\"بدء إنشاء نسخة احتياطية من نوع: {backup_type}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:55:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:55:01.146488",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:55:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:55:01.147513",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:55:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:55:20.664394",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:17.586764",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:18.598716",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:18.603794",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:19.688673",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:19.689943",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:58:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:58:20.707889",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:58:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:58:20.711209",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:59:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:59:15.531614",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:33 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:33.495407",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:34.532822",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:34.539164",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:35.569617",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:35.571121",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:47.253931",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:18.713496",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:19.724059",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:19.729677",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:20.767178",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:20.768443",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:30.834248",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:29.616835",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:31.174922",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:31.191531",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:32.461898",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:32.464840",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:19:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:19:08.132079",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:13:33 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:13:33.105230",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:13:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:13:35.519151",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:13:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:13:35.531270",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:13:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:13:37.509931",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:13:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:13:37.515128",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:14:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:14:38.540275",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:14:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:14:38.544558",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:15:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:15:13.675292",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في إنشاء تقرير الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 323, in get_performance_report\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:15:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:15:17.247046",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء النسخة الاحتياطية",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 62, in create_backup\n    smart_logger.log_info(f\"بدء إنشاء نسخة احتياطية من نوع: {backup_type}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:15:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:15:39.567061",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:15:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:15:39.571615",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:16:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:16:40.591841",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:16:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:16:40.595723",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:17:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:17:15.153589",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:17:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:17:20.513864",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:17:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:17:22.239599",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:17:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:17:22.247060",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:17:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:17:23.950480",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:17:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:17:23.957093",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:17:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:17:56.843617",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:18:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:18:00.155573",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:18:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:18:01.769728",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:18:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:18:01.779618",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:18:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:18:03.099171",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:18:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:18:03.102694",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:19:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:19:04.122981",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:19:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:19:04.125992",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:19:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:19:38.281944",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:19:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:19:45.853273",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:19:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:19:47.681027",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:19:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:19:47.688081",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:19:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:19:49.060379",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:19:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:19:49.064696",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:20:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:20:16.063307",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:20:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:20:19.389235",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:20:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:20:21.193995",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:20:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:20:21.202398",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:20:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:20:22.626592",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:20:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:20:22.631999",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:20:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:20:35.500173",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:17.395216",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:18.399703",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:18.405089",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:19.444864",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:19.446050",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:50 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:50.116217",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:51.130729",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:51.136555",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:52.191673",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:21:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:21:52.193172",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:22:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:22:05.585623",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:22:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:22:08.042605",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:22:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:22:09.012868",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:22:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:22:09.018813",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:22:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:22:10.118207",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:22:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:22:10.119601",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:23:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:23:10.123076",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:23:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:23:13.293732",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:23:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:23:14.378067",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:23:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:23:14.383042",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:23:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:23:15.392754",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:23:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:23:15.395346",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:23:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:23:26.251116",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:24:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:24:09.921995",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:24:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:24:10.954610",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:24:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:24:10.959841",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:24:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:24:11.989209",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:24:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:24:11.990811",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:25:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:25:13.005505",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:25:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:25:13.009871",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:26:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:26:02.092015",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:27:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:27:07.554597",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:27:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:27:08.597208",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:27:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:27:08.602788",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:27:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:27:09.646110",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:27:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:27:09.647310",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:10.662151",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:10.665131",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:23.327009",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:24.859924",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:25.882188",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:25.887845",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:26.996766",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:26.998128",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:42.998659",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:44.062131",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:44.067845",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:45.215169",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:28:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:28:45.216456",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:01.864383",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:02.861549",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:02.867562",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:03.887162",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:03.889471",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:27.129360",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:28.136504",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:28.142514",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:29.215783",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:29:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:29:29.217049",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:30:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:30:30.228936",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:30:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:30:30.230087",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:31:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:31:04.827883",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:31:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:31:08.159728",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:31:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:31:09.221074",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:31:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:31:09.228436",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:31:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:31:10.329146",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:31:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:31:10.330690",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:07.623976",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:08.935605",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:09.933791",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:09.938729",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:10.976123",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:10.977602",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:22.625442",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:27.845844",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:28.837543",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:28.843546",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:29.866932",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:32:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:32:29.868103",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:33:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:33:30.482902",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:33:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:33:31.470482",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:33:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:33:31.477644",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:33:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:33:32.507835",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:33:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:33:32.509257",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:34:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:34:19.444648",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:34:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:34:24.656265",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:34:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:34:25.615290",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:34:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:34:25.621026",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:34:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:34:26.625444",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:34:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:34:26.626632",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:35:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:35:27.642045",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:35:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:35:27.645063",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:35:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:35:56.013847",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:36:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:36:13.801712",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:36:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:36:14.828486",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:36:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:36:14.834019",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:36:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:36:15.884628",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:36:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:36:15.885985",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:36:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:36:41.712662",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:37:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:37:05.463927",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:37:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:37:06.480495",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:37:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:37:06.485684",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:37:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:37:07.575425",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:37:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:37:07.576721",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:38:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:38:08.591899",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:38:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:38:08.594523",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:39:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:39:09.605777",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:39:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:39:09.607016",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:39:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:39:52.209754",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:39:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:39:53.250253",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:39:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:39:53.255935",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:39:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:39:54.303826",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:39:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:39:54.305299",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:40:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:40:55.321493",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:40:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:40:55.322459",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:41:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:41:17.316079",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:41:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:41:17.986412",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:41:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:41:21.995130",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:41:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:41:22.967519",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:41:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:41:22.972845",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:41:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:41:24.026051",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:41:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:41:24.027536",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:42:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:42:25.040452",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:42:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:42:25.043684",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:43:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:43:26.058651",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:43:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:43:26.059700",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:43:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:43:38.550010",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:43:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:43:45.152786",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:43:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:43:46.224426",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:43:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:43:46.229589",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:43:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:43:47.235432",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:43:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:43:47.236636",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:44:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:44:13.346944",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:44:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:44:15.526814",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:44:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:44:16.585565",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:44:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:44:16.592068",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:44:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:44:17.645002",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:44:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:44:17.646481",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:45:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:45:18.657176",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:45:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:45:18.658195",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:46:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:46:19.669507",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:46:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:46:19.670522",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:47:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:47:20.680748",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:47:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:47:20.681918",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:48:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:48:22.567568",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:48:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:48:23.623013",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:48:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:48:23.628945",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:48:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:48:24.685478",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:48:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:48:24.686890",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:48:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:48:59.241371",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:49:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:49:00.260211",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:49:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:49:00.265385",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:49:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:49:01.272277",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:49:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:49:01.273521",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:49:33 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:49:33.707154",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:49:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:49:34.709987",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:49:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:49:34.715528",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:49:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:49:35.729048",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:49:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:49:35.730590",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:00.322324",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:05.028389",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:06.446887",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:07.515036",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:07.516282",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:33 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:33.108088",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:34.104518",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:34.111081",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:35.120168",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:50:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:50:35.121835",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:51:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:51:36.133518",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:51:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:51:36.134757",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:52:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:52:37.142688",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:52:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:52:37.143719",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:52:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:52:42.488983",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:52:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:52:43.577572",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:52:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:52:44.588564",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:52:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:52:44.591313",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:53:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:53:09.395079",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:53:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:53:10.422459",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:53:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:53:10.428847",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:53:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:53:11.444811",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:53:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:53:11.446321",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:54:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:54:12.458388",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:54:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:54:12.459398",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:54:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:54:27.264953",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:54:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:54:28.360349",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:54:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:54:29.372227",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:54:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:54:29.374530",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:55:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:55:16.317327",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:55:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:55:17.336962",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:55:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:55:17.342554",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:55:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:55:18.484073",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:55:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:55:18.485410",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:56:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:56:18.489870",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:56:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:56:19.392768",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:56:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:56:20.493763",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:56:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:56:21.503780",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:56:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:56:21.505230",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:57:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:57:21.433415",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:57:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:57:22.450218",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:57:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:57:22.455828",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:57:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:57:23.497186",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:57:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:57:23.498629",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:57:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:57:49.589894",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:57:50 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:57:50.657246",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:57:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:57:51.810538",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:57:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:57:51.812882",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:05.029910",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:06.021563",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:06.027185",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:07.123463",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:07.124780",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:07.810988",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:52.651783",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:53.681344",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:53.686143",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:54.692723",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:58:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:58:54.695019",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:59:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:59:55.709777",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 01:59:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T01:59:55.712362",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:01.672610",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:13.632536",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:14.651185",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:14.657439",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:15.673483",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:15.674858",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:31.512024",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:32.533074",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:32.538271",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:33 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:33.703729",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:33 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:33.705281",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:52.059137",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:53.054649",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:53.060338",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:54.086935",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:00:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:00:54.088261",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:41.811810",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:42.807080",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:42.813190",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:43.881794",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:43.883218",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:56.962311",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:57 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:57.986359",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:57 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:57.992145",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:58 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:58.998234",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:01:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:01:58.999864",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:02:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:02:51.482262",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:02:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:02:52.496368",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:02:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:02:52.502135",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:02:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:02:53.578220",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:02:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:02:53.579561",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:07.946330",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:09.029271",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:10.034133",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:10.035681",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:13.306327",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:36.551697",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:37.537650",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:37.543012",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:38.547720",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:38.548961",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:39.219067",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:40.269126",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:40.277129",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:41.455612",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:03:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:03:41.456908",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:04:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:04:42.472465",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:04:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:04:42.473457",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:04:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:04:55.691599",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:04.564217",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:05.624849",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:05.629903",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:06.648619",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:06.650013",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:40.222213",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:41.257043",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:41.262513",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:42.344816",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:06:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:06:42.346152",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:07:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:07:43.357007",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:07:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:07:43.358186",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:15.378217",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:20.688304",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:21.741319",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:21.746777",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:22.768292",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:22.770989",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:45.894023",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:53.618506",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:54.592089",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:54.597880",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:55.617339",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:08:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:08:55.618599",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:09:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:09:39.117745",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:09:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:09:40.193590",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:09:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:09:40.199592",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:09:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:09:41.237704",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:09:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:09:41.239045",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:10:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:10:10.682266",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:10:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:10:11.661073",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:10:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:10:11.666709",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:10:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:10:12.672755",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:10:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:10:12.674084",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-21 02:10:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-21T02:10:21.995458",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
